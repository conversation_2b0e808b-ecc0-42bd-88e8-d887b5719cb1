import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>R<PERSON>, Zap, Shield, Sparkles, TrendingUp } from 'lucide-react';

const PopularCombinations: React.FC = () => {
  const powerPairings = [
    {
      slug: 'vitamin-c-ferulic-acid-vitamin-e',
      ingredients: ['Vitamin C', 'Ferulic Acid', 'Vitamin E'],
      title: 'The Antioxidant Powerhouse',
      description: 'This legendary trio provides superior antioxidant protection while stabilizing vitamin C for maximum efficacy.',
      benefits: ['Enhanced stability', 'Superior protection', 'Brightening boost'],
      icon: Shield,
      color: 'from-orange-400 to-red-500',
      popularity: 95,
      isTrending: true,
    },
    {
      slug: 'niacinamide-retinol',
      ingredients: ['Niacinamide', 'Retinol'],
      title: 'The Gentle Anti-Aging Duo',
      description: 'Niacinamide helps reduce retinol irritation while both ingredients work together for smoother, younger-looking skin.',
      benefits: ['Reduced irritation', 'Enhanced tolerance', 'Anti-aging power'],
      icon: Sparkles,
      color: 'from-purple-400 to-pink-500',
      popularity: 88,
      isTrending: true,
    },
    {
      slug: 'hyaluronic-acid-ceramides',
      ingredients: ['Hyaluronic Acid', 'Ceramides'],
      title: 'The Hydration Heroes',
      description: 'The perfect moisture-locking combination that hydrates deeply while strengthening your skin barrier.',
      benefits: ['Deep hydration', 'Barrier repair', 'Long-lasting moisture'],
      icon: Zap,
      color: 'from-blue-400 to-teal-500',
      popularity: 91,
      isTrending: false,
    },
  ];

  return (
    <section className="section-padding bg-brand-off-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Power Pairings
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover scientifically-proven ingredient combinations that work synergistically 
            to deliver enhanced results for your skin.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {powerPairings.map((pairing, index) => (
            <Link
              key={pairing.slug}
              to={`/combinations/${pairing.slug}`}
              className="card overflow-hidden hover:scale-105 transition-all duration-300 group animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Header with gradient */}
              <div className={`bg-gradient-to-r ${pairing.color} p-6 text-white relative`}>
                <div className="flex items-start justify-between mb-4">
                  <pairing.icon className="w-8 h-8" />
                  <div className="flex items-center space-x-2">
                    {pairing.isTrending && (
                      <TrendingUp className="w-4 h-4" />
                    )}
                    <span className="text-sm bg-white/20 px-2 py-1 rounded-full">
                      {pairing.popularity}% match
                    </span>
                  </div>
                </div>
                
                <h3 className="text-xl font-bold mb-2">
                  {pairing.title}
                </h3>
                
                <div className="flex flex-wrap gap-2">
                  {pairing.ingredients.map((ingredient, idx) => (
                    <span
                      key={idx}
                      className="text-sm bg-white/20 px-3 py-1 rounded-full"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {pairing.description}
                </p>

                <div className="space-y-2 mb-6">
                  <div className="text-sm font-medium text-gray-700">Key Benefits:</div>
                  <div className="flex flex-wrap gap-2">
                    {pairing.benefits.map((benefit, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md"
                      >
                        {benefit}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    Learn more about this pairing
                  </span>
                  <ArrowRight className="w-5 h-5 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Visual Connection Diagram */}
        <div className="bg-white rounded-2xl p-8 mb-12 shadow-sm">
          <h3 className="text-2xl font-bold text-brand-charcoal mb-8 text-center">
            How Ingredients Connect
          </h3>
          
          <div className="flex items-center justify-center space-x-8 overflow-x-auto">
            {/* Simplified visual representation */}
            <div className="flex items-center space-x-4 min-w-max">
              <div className="w-20 h-20 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm text-center">
                Vitamin C
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-xs text-center">
                Ferulic Acid
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm text-center">
                Vitamin E
              </div>
            </div>
          </div>
          
          <p className="text-center text-gray-600 mt-6 max-w-2xl mx-auto">
            Each connection represents a scientifically-proven synergy that enhances 
            the effectiveness of individual ingredients when used together.
          </p>
        </div>

        <div className="text-center">
          <Link to="/combinations" className="btn-primary">
            Explore All Combinations
          </Link>
        </div>
      </div>
    </section>
  );
};

export default PopularCombinations;