import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { <PERSON>u, X, Beaker, Book, BarChart3, Wrench, FileText } from 'lucide-react';

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const navigationItems = [
    { label: 'Ingredient Directory', href: '/ingredients', icon: Beaker },
    { label: 'Combination Guides', href: '/combinations', icon: BarChart3 },
    { label: "Beginner's Hub", href: '/beginners', icon: Book },
    { label: 'Tools', href: '/tools', icon: Wrench },
    { label: 'Blog', href: '/blog', icon: FileText },
  ];

  const isActiveLink = (href: string) => {
    if (href === '/' && location.pathname === '/') return true;
    if (href !== '/' && location.pathname.startsWith(href)) return true;
    return false;
  };

  return (
    <header className="fixed w-full top-0 z-50 glass-effect">
      <div className="container-custom">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-brand-teal rounded-lg flex items-center justify-center">
              <Beaker className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-brand-charcoal">
              Skincare Compass
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <Link
                key={item.label}
                to={item.href}
                className={`flex items-center space-x-2 transition-colors duration-200 ${
                  isActiveLink(item.href)
                    ? 'text-brand-teal'
                    : 'text-brand-charcoal hover:text-brand-teal'
                }`}
              >
                <item.icon className="w-4 h-4" />
                <span className="font-medium">{item.label}</span>
              </Link>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 text-brand-charcoal hover:text-brand-teal transition-colors duration-200"
            >
              {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100 animate-slide-up">
            <nav className="flex flex-col space-y-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.label}
                  to={item.href}
                  className={`flex items-center space-x-3 p-3 rounded-lg transition-colors duration-200 ${
                    isActiveLink(item.href)
                      ? 'bg-brand-teal/10 text-brand-teal'
                      : 'hover:bg-gray-50 text-brand-charcoal'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <item.icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;