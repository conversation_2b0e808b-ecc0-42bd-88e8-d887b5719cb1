import React from 'react';
import { Target, Zap, Droplets, Flame, Sparkles, AlertTriangle } from 'lucide-react';

const SkinConcernsTargetingGuide: React.FC = () => {
  const concerns = [
    {
      name: 'Hyperpigmentation',
      icon: Target,
      color: 'bg-amber-500',
      textColor: 'text-amber-800',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      ingredients: [
        { name: 'Vitamin C', efficacy: 92, timeframe: '8-12 weeks' },
        { name: 'Tranexamic Acid', efficacy: 87, timeframe: '8-12 weeks' },
        { name: 'Alpha Arbutin', efficacy: 78, timeframe: '12+ weeks' },
        { name: 'Kojic Acid', efficacy: 75, timeframe: '12+ weeks' },
        { name: 'Niacinamide', efficacy: 70, timeframe: '8-12 weeks' },
      ],
      tips: [
        'Always use sunscreen',
        'Consistency is key',
        'Combine ingredients for best results',
      ],
    },
    {
      name: '<PERSON>c<PERSON>',
      icon: Flame,
      color: 'bg-red-500',
      textColor: 'text-red-800',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      ingredients: [
        { name: 'Salicylic Acid', efficacy: 90, timeframe: '4-8 weeks' },
        { name: 'Benzoyl Peroxide', efficacy: 88, timeframe: '4-6 weeks' },
        { name: 'Azelaic Acid', efficacy: 85, timeframe: '8-12 weeks' },
        { name: 'Niacinamide', efficacy: 82, timeframe: '8 weeks' },
        { name: 'Retinoids', efficacy: 80, timeframe: '12 weeks' },
      ],
      tips: [
        'Don\'t over-cleanse',
        'Avoid picking or squeezing',
        'Hydration is still important',
      ],
    },
    {
      name: 'Fine Lines & Wrinkles',
      icon: Sparkles,
      color: 'bg-purple-500',
      textColor: 'text-purple-800',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      ingredients: [
        { name: 'Retinol', efficacy: 95, timeframe: '12+ weeks' },
        { name: 'Peptides', efficacy: 85, timeframe: '12+ weeks' },
        { name: 'Vitamin C', efficacy: 80, timeframe: '12+ weeks' },
        { name: 'Bakuchiol', efficacy: 75, timeframe: '12+ weeks' },
        { name: 'AHAs', efficacy: 70, timeframe: '8-12 weeks' },
      ],
      tips: [
        'Prevention is easier than correction',
        'Sunscreen is your best anti-aging tool',
        'Consistency over time yields results',
      ],
    },
    {
      name: 'Dryness & Dehydration',
      icon: Droplets,
      color: 'bg-blue-500',
      textColor: 'text-blue-800',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      ingredients: [
        { name: 'Hyaluronic Acid', efficacy: 95, timeframe: 'Immediate' },
        { name: 'Glycerin', efficacy: 90, timeframe: 'Immediate' },
        { name: 'Ceramides', efficacy: 88, timeframe: '2-4 weeks' },
        { name: 'Squalane', efficacy: 85, timeframe: 'Immediate' },
        { name: 'Panthenol', efficacy: 82, timeframe: 'Immediate' },
      ],
      tips: [
        'Apply to damp skin',
        'Use humidifier in dry climates',
        'Layer hydrating products',
      ],
    },
    {
      name: 'Sensitivity & Redness',
      icon: AlertTriangle,
      color: 'bg-green-500',
      textColor: 'text-green-800',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      ingredients: [
        { name: 'Centella Asiatica', efficacy: 90, timeframe: '2-4 weeks' },
        { name: 'Azelaic Acid', efficacy: 85, timeframe: '4-8 weeks' },
        { name: 'Niacinamide', efficacy: 82, timeframe: '4-8 weeks' },
        { name: 'Oat Extract', efficacy: 80, timeframe: 'Immediate' },
        { name: 'Allantoin', efficacy: 75, timeframe: '2-4 weeks' },
      ],
      tips: [
        'Minimize active ingredients',
        'Patch test everything',
        'Focus on barrier repair',
      ],
    },
    {
      name: 'Enlarged Pores',
      icon: Zap,
      color: 'bg-teal-500',
      textColor: 'text-teal-800',
      bgColor: 'bg-teal-50',
      borderColor: 'border-teal-200',
      ingredients: [
        { name: 'Niacinamide', efficacy: 88, timeframe: '4-8 weeks' },
        { name: 'Retinol', efficacy: 85, timeframe: '12+ weeks' },
        { name: 'Salicylic Acid', efficacy: 82, timeframe: '4-8 weeks' },
        { name: 'Clay', efficacy: 75, timeframe: 'Temporary' },
        { name: 'AHAs', efficacy: 70, timeframe: '8-12 weeks' },
      ],
      tips: [
        'Double cleanse to remove oil',
        'Don\'t skip moisturizer',
        'Consistent exfoliation helps',
      ],
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <Target className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Targeted Solutions for Common Skin Concerns
        </h2>
        <p className="text-gray-600">
          Science-backed ingredients ranked by effectiveness for specific concerns
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {concerns.map((concern, index) => (
          <div 
            key={index} 
            className={`rounded-xl border ${concern.borderColor} ${concern.bgColor} overflow-hidden animate-slide-up`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`${concern.color} p-4 text-white`}>
              <div className="flex items-center space-x-3">
                <concern.icon className="w-6 h-6" />
                <h3 className="text-lg font-bold">{concern.name}</h3>
              </div>
            </div>
            
            <div className="p-4">
              <div className="space-y-3 mb-4">
                {concern.ingredients.map((ingredient, idx) => (
                  <div key={idx} className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className={`font-medium ${concern.textColor}`}>{ingredient.name}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">{ingredient.timeframe}</span>
                        <span className="text-xs font-medium">{ingredient.efficacy}%</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`${concern.color} h-2 rounded-full`} 
                        style={{ width: `${ingredient.efficacy}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="bg-white rounded-lg p-3 border border-gray-100">
                <h4 className={`text-xs font-medium ${concern.textColor} mb-2`}>Pro Tips:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  {concern.tips.map((tip, idx) => (
                    <li key={idx} className="flex items-start space-x-1">
                      <div className={`w-1.5 h-1.5 ${concern.color} rounded-full mt-1 flex-shrink-0`}></div>
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold text-brand-charcoal mb-3">Understanding Efficacy Ratings:</h3>
        <p className="text-sm text-gray-600 mb-4">
          Efficacy percentages are based on clinical studies, research data, and expert consensus. 
          Individual results may vary based on skin type, product formulation, and consistent use.
        </p>
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div className="bg-white rounded-lg p-3 border border-gray-100">
            <span className="font-medium text-brand-charcoal">90%+ Efficacy</span>
            <p className="text-gray-600 text-xs">Gold standard ingredients with substantial research</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-100">
            <span className="font-medium text-brand-charcoal">80-89% Efficacy</span>
            <p className="text-gray-600 text-xs">Highly effective with strong clinical backing</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-100">
            <span className="font-medium text-brand-charcoal">70-79% Efficacy</span>
            <p className="text-gray-600 text-xs">Effective supporting ingredients</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkinConcernsTargetingGuide;