import React from 'react';
import { Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

const SkincareProductShelfLife: React.FC = () => {
  const productCategories = [
    {
      category: 'Serums & Treatments',
      products: [
        { name: 'Vitamin C Serum', shelfLife: '3-6 months', openLife: '1-3 months', signs: 'Darkening to yellow/brown, oxidized smell', storage: 'Refrigerator, dark bottle' },
        { name: 'Retinol Products', shelfLife: '6-12 months', openLife: '6 months', signs: 'Change in color, texture, or smell', storage: 'Cool, dark place, airless pump' },
        { name: 'Hyaluronic Acid Serum', shelfLife: '12 months', openLife: '6-12 months', signs: 'Change in texture, cloudy appearance', storage: 'Room temperature, closed tightly' },
        { name: 'AHA/BHA Exfoliants', shelfLife: '12 months', openLife: '6-12 months', signs: 'Change in color or smell, reduced efficacy', storage: 'Room temperature, away from light' },
      ],
    },
    {
      category: 'Moisturizers & Creams',
      products: [
        { name: 'Water-Based Moisturizer', shelfLife: '12 months', openLife: '6-12 months', signs: 'Separation, change in texture or smell', storage: 'Room temperature, closed tightly' },
        { name: 'Oil-Based Moisturizer', shelfLife: '12-18 months', openLife: '6-12 months', signs: 'Rancid smell, separation', storage: 'Cool, dark place' },
        { name: 'Eye Cream', shelfLife: '6-12 months', openLife: '3-6 months', signs: 'Change in texture or smell', storage: 'Refrigerator for cooling effect' },
        { name: 'Night Cream', shelfLife: '12 months', openLife: '6-12 months', signs: 'Separation, change in texture or smell', storage: 'Room temperature, closed tightly' },
      ],
    },
    {
      category: 'Cleansers & Masks',
      products: [
        { name: 'Gel/Foam Cleanser', shelfLife: '12-24 months', openLife: '12 months', signs: 'Change in texture, smell, or color', storage: 'Room temperature' },
        { name: 'Oil Cleanser', shelfLife: '12 months', openLife: '6-12 months', signs: 'Rancid smell, separation', storage: 'Cool, dark place' },
        { name: 'Clay Mask', shelfLife: '12-24 months', openLife: '6-12 months', signs: 'Drying out, change in smell', storage: 'Room temperature, tightly sealed' },
        { name: 'Sheet Masks', shelfLife: '24-36 months', openLife: 'Single use', signs: 'Dryness, discoloration', storage: 'Cool, dark place' },
      ],
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <Clock className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Skincare Product Shelf Life Guide
        </h2>
        <p className="text-gray-600">
          How long products last and how to tell when they've expired
        </p>
      </div>

      <div className="space-y-8">
        {productCategories.map((category, index) => (
          <div key={index} className="animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
            <h3 className="text-xl font-bold text-brand-charcoal mb-4">{category.category}</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="p-3 text-left text-sm font-semibold text-brand-charcoal border border-gray-200">Product</th>
                    <th className="p-3 text-center text-sm font-semibold text-brand-charcoal border border-gray-200">Unopened Shelf Life</th>
                    <th className="p-3 text-center text-sm font-semibold text-brand-charcoal border border-gray-200">After Opening</th>
                    <th className="p-3 text-left text-sm font-semibold text-brand-charcoal border border-gray-200">Signs of Expiration</th>
                    <th className="p-3 text-left text-sm font-semibold text-brand-charcoal border border-gray-200">Storage Tips</th>
                  </tr>
                </thead>
                <tbody>
                  {category.products.map((product, idx) => (
                    <tr key={idx} className={idx % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                      <td className="p-3 border border-gray-200 font-medium text-brand-charcoal">{product.name}</td>
                      <td className="p-3 border border-gray-200 text-center">{product.shelfLife}</td>
                      <td className="p-3 border border-gray-200 text-center">{product.openLife}</td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">{product.signs}</td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">{product.storage}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 grid md:grid-cols-3 gap-6">
        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
          <div className="flex items-start space-x-3">
            <CheckCircle className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-green-800 mb-2">How to Extend Shelf Life</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Store products away from direct sunlight</li>
                <li>• Keep containers tightly closed</li>
                <li>• Use clean hands or spatulas</li>
                <li>• Refrigerate unstable ingredients</li>
                <li>• Follow product-specific storage instructions</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-1 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-yellow-800 mb-2">When to Be Cautious</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Product has changed color or texture</li>
                <li>• Unusual or rancid smell</li>
                <li>• Separation that doesn't remix</li>
                <li>• Reduced efficacy</li>
                <li>• Irritation when using previously tolerated product</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4 border border-red-200">
          <div className="flex items-start space-x-3">
            <XCircle className="w-5 h-5 text-red-600 mt-1 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-red-800 mb-2">When to Discard</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• Visible mold or bacterial growth</li>
                <li>• Strong rancid or "off" smell</li>
                <li>• Significant change in color (especially vitamin C)</li>
                <li>• Unusual texture changes (clumping, separation)</li>
                <li>• Product causes irritation, redness, or burning</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkincareProductShelfLife;