/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
      },
      colors: {
        // Brand colors
        'brand-white': '#FFFFFF',
        'brand-off-white': '#F8F9FA',
        'brand-charcoal': '#212529',
        'brand-teal': '#20C997',
        'brand-teal-light': '#40E0D0',
        'brand-teal-dark': '#0D9488',
        // Category colors
        'category-pink': '#F8BBD9',
        'category-lavender': '#E5CCFF',
        'category-green': '#BBF7D0',
        'category-blue': '#BFDBFE',
        'category-yellow': '#FDE68A',
        'category-orange': '#FED7AA',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'float': 'float 6s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
    },
  },
  plugins: [],
};