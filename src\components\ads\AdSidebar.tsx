import React from 'react';
import AdBanner from './AdBanner';

interface AdSidebarProps {
  className?: string;
}

const AdSidebar: React.FC<AdSidebarProps> = ({ className = '' }) => {
  const fallbackContent = (
    <div className="bg-gradient-to-br from-brand-teal/10 to-brand-teal/5 border border-brand-teal/20 rounded-lg p-4 text-center">
      <div className="text-brand-teal font-medium mb-2">Skincare Tips</div>
      <div className="text-sm text-gray-600">
        Always patch test new ingredients before adding them to your routine.
      </div>
    </div>
  );

  return (
    <div className={`ad-sidebar ${className}`}>
      <div className="sticky top-20 space-y-4">
        <AdBanner
          width={160}
          height={300}
          adKey="6c9ee3d6f78d0d206bfe9972fc77e6be"
          className="mx-auto"
          fallbackContent={fallbackContent}
        />
        
        {/* Additional sidebar content */}
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
          <h3 className="font-semibold text-brand-charcoal mb-2">Quick Tips</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Start with basic routine</li>
            <li>• Introduce one new product at a time</li>
            <li>• Always use sunscreen</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AdSidebar;
