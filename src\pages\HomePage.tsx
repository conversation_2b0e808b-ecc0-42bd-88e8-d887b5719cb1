import React from 'react';
import Hero from '../components/sections/Hero';
import FeaturedIngredients from '../components/sections/FeaturedIngredients';
import BeginnersHubCTA from '../components/sections/BeginnersHubCTA';
import PopularCombinations from '../components/sections/PopularCombinations';
import FeaturedTools from '../components/sections/FeaturedTools';
import LatestBlog from '../components/sections/LatestBlog';
import NewsletterSignup from '../components/sections/NewsletterSignup';
import MetaTags from '../components/SEO/MetaTags';
import SkincareTrendRadar from '../components/infographics/SkincareTrendRadar';
import SkincareIngredientsWheel from '../components/infographics/SkincareIngredientsWheel';

const HomePage: React.FC = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Skincare Compass - The Ultimate Ingredient Encyclopedia",
    "description": "The world's most trusted resource for skincare ingredient information, combination guides, and beginner-friendly education.",
    "url": "https://www.skincarecompass.com/",
    "mainEntity": {
      "@type": "Organization",
      "name": "Skincare Compass",
      "description": "Expert skincare ingredient database and education platform"
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.skincarecompass.com/"
        }
      ]
    }
  };

  return (
    <>
      <MetaTags
        title="Skincare Compass - The Ultimate Ingredient Encyclopedia & Guide"
        description="The world's most trusted resource for skincare ingredient information, combination guides, and beginner-friendly education. Discover 2,500+ ingredients, expert combinations, and science-backed advice."
        keywords="skincare ingredients, skincare routine, ingredient compatibility, skincare guide, skincare science, dermatology, cosmetic chemistry, skincare education"
        canonicalUrl="https://www.skincarecompass.com/"
        structuredData={structuredData}
      />
      <Hero />
      <FeaturedIngredients />
      <div className="section-padding bg-white">
        <div className="container-custom">
          <SkincareTrendRadar />
        </div>
      </div>
      <BeginnersHubCTA />
      <div className="section-padding bg-white">
        <div className="container-custom">
          <SkincareIngredientsWheel />
        </div>
      </div>
      <PopularCombinations />
      <FeaturedTools />
      <LatestBlog />
      <NewsletterSignup />
    </>
  );
};

export default HomePage;