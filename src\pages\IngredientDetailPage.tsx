import React, { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useLocation } from 'react-router-dom';
import { ArrowLeft, ArrowRight, Beaker, Star, TrendingUp, AlertTriangle, CheckCircle, Info, ExternalLink, Droplets, Shield, Zap, Sparkles, Clock, Users, BookOpen, FlaskConical, Activity, Eye, HelpCircle } from 'lucide-react';
import { ingredients } from '../data/ingredients';
import { additionalIngredients } from '../data/additionalIngredients';
import { enhanceWithFAQs } from '../data/additionalIngredientsWithFAQs';
import MetaTags from '../components/SEO/MetaTags';
import IngredientCompatibilityMatrix from '../components/infographics/IngredientCompatibilityMatrix';
import IngredientPenetrationChart from '../components/infographics/IngredientPenetrationChart';
import SkinConcernsTargetingGuide from '../components/infographics/SkinConcernsTargetingGuide';
import { AdSidebar } from '../components/ads';

const IngredientDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const location = useLocation();
  
  // Scroll to top when navigating to a new page
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);
  
  // Get ingredient data from our data files
  const mainIngredient = slug ? ingredients[slug] : null;
  const additionalIngredient = slug ? additionalIngredients.find(ing => ing.slug === slug) : null;
  
  // Use the main ingredient if available, otherwise use the additional ingredient
  const ingredient = mainIngredient || (additionalIngredient ? enhanceWithFAQs(additionalIngredient) : null);

  if (!ingredient) {
    return (
      <div className="pt-16 min-h-screen bg-brand-off-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-brand-charcoal mb-4">Ingredient Not Found</h1>
          <Link to="/ingredients" className="btn-primary">
            Browse All Ingredients
          </Link>
        </div>
      </div>
    );
  }

  const getIcon = (iconName: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      Droplets,
      Shield,
      Zap,
      CheckCircle,
      Sparkles,
    };
    return icons[iconName] || CheckCircle;
  };

  // SEO Data
  const seoTitle = `${ingredient.name} for Skin: Complete Guide, Benefits & Scientific Research`;
  const seoDescription = `Comprehensive guide to ${ingredient.name} in skincare. Learn benefits, usage, scientific research, clinical studies, and safety profile. Expert-reviewed information with original research links.`;
  const seoKeywords = `${ingredient.name}, ${ingredient.name} skincare, ${ingredient.name} benefits, ${ingredient.name} research, ${ingredient.category.toLowerCase()}, skincare ingredients, ${mainIngredient?.nicknames?.join(', ') || ''}`;
  const canonicalUrl = `https://www.skincarecompass.com/ingredients/${slug}/`;

  // Structured Data for Product Schema
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": seoTitle,
    "description": seoDescription,
    "image": `https://www.skincarecompass.com/images/ingredients/${slug}.jpg`,
    "author": {
      "@type": "Organization",
      "name": "Skincare Compass"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Skincare Compass",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.skincarecompass.com/logo.png"
      }
    },
    "datePublished": "2025-06-15",
    "dateModified": "2025-06-15",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    },
    "about": {
      "@type": "Thing",
      "name": ingredient.name,
      "description": ingredient.description
    },
    "mentions": mainIngredient?.combinations?.map(combo => ({
      "@type": "Thing",
      "name": `${ingredient.name} + ${combo.ingredients.join(' + ')}`
    })) || []
  };

  // FAQ Structured Data
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": (mainIngredient?.faqs || 'faqs' in ingredient ? ingredient.faqs : []).map((faq: any) => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  // Render appropriate infographic based on ingredient type
  const renderInfographic = () => {
    if (ingredient.category === 'Hydration' || ingredient.type === 'Humectant' || ingredient.type === 'Emollient') {
      return <IngredientPenetrationChart />;
    } else if (ingredient.category === 'Anti-Aging' || ingredient.category === 'Brightening') {
      return <SkinConcernsTargetingGuide />;
    } else {
      return <IngredientCompatibilityMatrix />;
    }
  };

  // Render different content based on whether it's a main or additional ingredient
  const renderContent = () => {
    if (mainIngredient) {
      return (
        <>
          {/* Mechanism of Action */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-brand-charcoal mb-4 flex items-center">
              <Activity className="w-6 h-6 text-brand-teal mr-3" />
              How It Works
            </h2>
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
              <p className="text-gray-700 leading-relaxed">
                {mainIngredient.mechanismOfAction}
              </p>
            </div>
          </div>
        </>
      );
    }
    
    return (
      <>
        {/* Additional ingredient content */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-brand-charcoal mb-4 flex items-center">
            <Activity className="w-6 h-6 text-brand-teal mr-3" />
            Key Functions
          </h2>
          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <ul className="space-y-2">
              {additionalIngredient?.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </>
    );
  };

  // Render sidebar based on ingredient type
  const renderSidebar = () => {
    if (mainIngredient) {
      return (
        <div className="card p-6 sticky top-24">
          <h3 className="text-lg font-semibold text-brand-charcoal mb-4">Quick Facts</h3>
          <div className="space-y-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Type:</span>
              <p className="text-gray-700">{mainIngredient.quickFacts.type}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Main Benefit:</span>
              <p className="text-gray-700">{mainIngredient.quickFacts.mainBenefit}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Concentration:</span>
              <p className="text-gray-700">{mainIngredient.quickFacts.concentration}</p>
            </div>
            {mainIngredient.quickFacts.pHRange && (
              <div>
                <span className="text-sm font-medium text-gray-500">pH Range:</span>
                <p className="text-gray-700">{mainIngredient.quickFacts.pHRange}</p>
              </div>
            )}
            {mainIngredient.quickFacts.molecularWeight && (
              <div>
                <span className="text-sm font-medium text-gray-500">Molecular Weight:</span>
                <p className="text-gray-700">{mainIngredient.quickFacts.molecularWeight}</p>
              </div>
            )}
            <div>
              <span className="text-sm font-medium text-gray-500">Best For:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {mainIngredient.quickFacts.bestFor.map((type, index) => (
                  <span key={index} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    {type}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Avoid Mixing With:</span>
              <div className="mt-1">
                {mainIngredient.quickFacts.avoidMixingWith.map((item, index) => (
                  <span key={index} className="text-sm text-gray-700 block">
                    {item}
                  </span>
                ))}
              </div>
            </div>
            <div className="pt-4 border-t border-gray-100">
              <div className="flex items-center space-x-1 mb-2">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="text-sm font-medium">Popularity: {mainIngredient.popularity}/100</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-brand-teal h-2 rounded-full" 
                  style={{ width: `${mainIngredient.popularity}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <div className="card p-6 sticky top-24">
        <h3 className="text-lg font-semibold text-brand-charcoal mb-4">Quick Facts</h3>
        <div className="space-y-4">
          <div>
            <span className="text-sm font-medium text-gray-500">Type:</span>
            <p className="text-gray-700">{additionalIngredient?.type}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Category:</span>
            <p className="text-gray-700">{additionalIngredient?.category}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Best For:</span>
            <div className="flex flex-wrap gap-1 mt-1">
              {additionalIngredient?.bestFor.map((type, index) => (
                <span key={index} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                  {type}
                </span>
              ))}
            </div>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Avoid Mixing With:</span>
            <div className="mt-1">
              {additionalIngredient?.avoidMixingWith.map((item, index) => (
                <span key={index} className="text-sm text-gray-700 block">
                  {item}
                </span>
              ))}
            </div>
          </div>
          <div className="pt-4 border-t border-gray-100">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Safety Profile:</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium text-gray-600">General Safety:</span>
                <p className="text-gray-700">{additionalIngredient?.safetyProfile.generalSafety}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Pregnancy Safety:</span>
                <p className="text-gray-700">{additionalIngredient?.safetyProfile.pregnancySafety}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Sensitivity Risk:</span>
                <p className="text-gray-700">{additionalIngredient?.safetyProfile.sensitivityRisk}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        canonicalUrl={canonicalUrl}
        structuredData={[structuredData, faqStructuredData]}
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link to="/" className="text-gray-500 hover:text-brand-teal">Home</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link to="/ingredients" className="text-gray-500 hover:text-brand-teal">Ingredients</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-brand-charcoal font-medium">{ingredient.name}</li>
            </ol>
          </nav>
          <Link
            to="/ingredients"
            className="inline-flex items-center space-x-2 text-brand-teal hover:text-brand-teal-dark transition-colors duration-200 mt-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Ingredients</span>
          </Link>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white">
        <div className="container-custom py-12">
          <div className="grid lg:grid-cols-4 gap-12">
            <div className="lg:col-span-3">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-brand-teal/10 rounded-xl flex items-center justify-center">
                  <Beaker className="w-8 h-8 text-brand-teal" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold text-brand-charcoal mb-2">
                    {ingredient.name}
                  </h1>
                  <div className="flex items-center space-x-4 text-gray-600">
                    <span className="font-medium">{ingredient.scientificName}</span>
                    {mainIngredient?.isTrending && (
                      <div className="flex items-center space-x-1 text-orange-500">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm">Trending</span>
                      </div>
                    )}
                  </div>
                  {mainIngredient?.nicknames && (
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-sm text-gray-500">Also known as:</span>
                      {mainIngredient.nicknames.map((nickname, index) => (
                        <span key={index} className="text-sm bg-gray-100 text-gray-700 px-2 py-1 rounded-md">
                          {nickname}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* What It Is */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-brand-charcoal mb-4">What It Is</h2>
                <p className="text-lg text-gray-600 leading-relaxed mb-4">
                  {ingredient.description}
                </p>
                {mainIngredient?.detailedDescription && (
                  <p className="text-gray-600 leading-relaxed">
                    {mainIngredient.detailedDescription}
                  </p>
                )}
              </div>

              {/* Mechanism of Action or Key Functions */}
              {renderContent()}
            </div>

            {/* Quick Facts Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              {renderSidebar()}
              <AdSidebar />
            </div>
          </div>
        </div>
      </div>

      {/* Infographic Section */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          {renderInfographic()}
        </div>
      </div>

      {/* Benefits Matrix */}
      {mainIngredient?.benefits && (
        <div className="section-padding bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                Benefits & Scientific Evidence
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {mainIngredient.benefits.map((benefit, index) => {
                  const IconComponent = getIcon(benefit.icon);
                  return (
                    <div key={index} className="card p-6">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-brand-teal/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <IconComponent className="w-6 h-6 text-brand-teal" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-brand-charcoal mb-2">
                            {benefit.title}
                          </h3>
                          <p className="text-gray-600 mb-3">{benefit.description}</p>
                          <div className="bg-green-50 rounded-lg p-3 border border-green-200">
                            <div className="flex items-center space-x-2 mb-1">
                              <FlaskConical className="w-4 h-4 text-green-600" />
                              <span className="text-sm font-medium text-green-800">Scientific Evidence</span>
                            </div>
                            <p className="text-sm text-green-700">{benefit.scientificEvidence}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* How to Use It */}
      {mainIngredient?.howToUse && (
        <div className="section-padding bg-brand-off-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                How to Use It
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-4 flex items-center">
                    <Clock className="w-5 h-5 text-blue-500 mr-2" />
                    Basic Usage
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <span className="text-sm font-medium text-gray-500">Routine:</span>
                      <p className="text-gray-700">{mainIngredient.howToUse.routine}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Application:</span>
                      <p className="text-gray-700">{mainIngredient.howToUse.application}</p>
                    </div>
                  </div>
                </div>

                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-4 flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    Pro Tips
                  </h3>
                  <ul className="space-y-3">
                    {mainIngredient.howToUse.tips.map((tip, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-brand-teal rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-600 text-sm">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Precautions */}
              <div className="card p-6 mt-8 border-2 border-yellow-200 bg-yellow-50">
                <h3 className="text-lg font-semibold text-yellow-800 mb-4 flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Important Precautions
                </h3>
                <ul className="space-y-2">
                  {mainIngredient.howToUse.precautions.map((precaution, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-yellow-700 text-sm">{precaution}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Powerful Combinations */}
      {mainIngredient?.combinations && mainIngredient.combinations.length > 0 && (
        <div className="section-padding bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                Powerful Combinations
              </h2>
              <div className="grid md:grid-cols-3 gap-6">
                {mainIngredient.combinations.map((combo, index) => (
                  <Link
                    key={index}
                    to={`/combinations/${combo.slug}`}
                    className="card p-6 hover:scale-105 transition-all duration-200 group"
                    aria-label={`Learn about ${mainIngredient.name} combined with ${combo.ingredients.join(' and ')}`}
                  >
                    <h3 className="text-lg font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200">
                      {mainIngredient.name} + {combo.ingredients.join(' + ')}
                    </h3>
                    <p className="text-sm font-medium text-gray-500 mb-2">{combo.title}</p>
                    <p className="text-gray-600 text-sm">{combo.description}</p>
                    <ArrowRight className="w-4 h-4 text-brand-teal mt-3 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Scientific Studies & Evidence */}
      {mainIngredient?.research && mainIngredient.research.length > 0 && (
        <div className="section-padding bg-brand-off-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                Scientific Studies & Research
              </h2>
              <div className="space-y-6">
                {mainIngredient.research.map((study, index) => (
                  <div key={index} className="card p-6">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-lg font-semibold text-brand-charcoal pr-4">
                        {study.title}
                      </h3>
                      {study.link && (
                        <a
                          href={study.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 text-brand-teal hover:text-brand-teal-dark transition-colors duration-200 flex-shrink-0"
                        >
                          <ExternalLink className="w-4 h-4" />
                          <span className="text-sm">View Study</span>
                        </a>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mb-3 text-sm text-gray-500">
                      <span>{study.journal}</span>
                      <span>•</span>
                      <span>{study.year}</span>
                      {study.pubmedId && (
                        <>
                          <span>•</span>
                          <span>PMID: {study.pubmedId}</span>
                        </>
                      )}
                    </div>
                    <p className="text-gray-600 mb-4">{study.summary}</p>
                    {study.keyFindings && (
                      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <h4 className="font-semibold text-blue-800 mb-2">Key Findings:</h4>
                        <ul className="space-y-1">
                          {study.keyFindings.map((finding, idx) => (
                            <li key={idx} className="text-sm text-blue-700 flex items-start space-x-2">
                              <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span>{finding}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Clinical Studies */}
      {mainIngredient?.clinicalStudies && mainIngredient.clinicalStudies.length > 0 && (
        <div className="section-padding bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                Clinical Trial Data
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {mainIngredient.clinicalStudies.map((study, index) => (
                  <div key={index} className="card p-6 border-2 border-green-200 bg-green-50">
                    <h3 className="text-lg font-semibold text-green-800 mb-3">{study.studyType}</h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-green-700">Participants:</span>
                        <span className="text-green-600 ml-2">{study.participants}</span>
                      </div>
                      <div>
                        <span className="font-medium text-green-700">Duration:</span>
                        <span className="text-green-600 ml-2">{study.duration}</span>
                      </div>
                      <div>
                        <span className="font-medium text-green-700">Results:</span>
                        <span className="text-green-600 ml-2">{study.results}</span>
                      </div>
                      <div>
                        <span className="font-medium text-green-700">Significance:</span>
                        <span className="text-green-600 ml-2">{study.significance}</span>
                      </div>
                      <div className="pt-2 border-t border-green-200">
                        <span className="text-xs text-green-600">{study.reference}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Safety Profile */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Safety Profile
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-brand-charcoal mb-4 flex items-center">
                  <Shield className="w-5 h-5 text-green-500 mr-2" />
                  General Safety
                </h3>
                <p className="text-gray-600 mb-4">
                  {mainIngredient?.safetyProfile?.generalSafety || additionalIngredient?.safetyProfile?.generalSafety}
                </p>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Pregnancy Safety:</span>
                    <p className="text-gray-700 text-sm">
                      {mainIngredient?.safetyProfile?.pregnancySafety || additionalIngredient?.safetyProfile?.pregnancySafety}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Sensitivity Risk:</span>
                    <p className="text-gray-700 text-sm">
                      {mainIngredient?.safetyProfile?.sensitivityRisk || additionalIngredient?.safetyProfile?.sensitivityRisk}
                    </p>
                  </div>
                </div>
              </div>

              {mainIngredient?.safetyProfile?.contraindications && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />
                    Contraindications & Side Effects
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <span className="text-sm font-medium text-gray-500">Contraindications:</span>
                      <ul className="mt-1 space-y-1">
                        {mainIngredient.safetyProfile.contraindications.map((item, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                            <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Possible Side Effects:</span>
                      <ul className="mt-1 space-y-1">
                        {mainIngredient.safetyProfile.sideEffects.map((effect, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                            <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span>{effect}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      {(mainIngredient?.faqs || 'faqs' in ingredient) && (
        <div className="section-padding bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                Frequently Asked Questions
              </h2>
              <div className="space-y-4">
                {(mainIngredient?.faqs || 'faqs' in ingredient ? ingredient.faqs : []).map((faq: any, index: number) => (
                  <div key={index} className="card p-6">
                    <div className="flex items-start space-x-3">
                      <HelpCircle className="w-5 h-5 text-brand-teal mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="text-lg font-semibold text-brand-charcoal mb-3">
                          {faq.question}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Related Ingredients */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Related Ingredients
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              {/* Get related ingredients from both main and additional ingredients */}
              {[...Object.values(ingredients), ...additionalIngredients]
                .filter(ing => 
                  ing.slug !== slug && 
                  ing.category === ingredient.category && 
                  ing.slug !== 'collagen' // Avoid duplicates
                )
                .slice(0, 3)
                .map((relatedIngredient, index) => (
                  <Link
                    key={index}
                    to={`/ingredients/${relatedIngredient.slug}`}
                    className="card p-6 hover:scale-105 transition-all duration-200 group"
                  >
                    <h3 className="text-lg font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200">
                      {relatedIngredient.name}
                    </h3>
                    <p className="text-sm text-gray-500 mb-2">{relatedIngredient.category}</p>
                    <p className="text-gray-600 text-sm line-clamp-3">
                      {relatedIngredient.description}
                    </p>
                    <ArrowRight className="w-4 h-4 text-brand-teal mt-3 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngredientDetailPage;