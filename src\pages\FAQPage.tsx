import React, { useState } from 'react';
import { Search, ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';

const FAQPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [openItems, setOpenItems] = useState<number[]>([]);

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'general', name: 'General' },
    { id: 'ingredients', name: 'Ingredients' },
    { id: 'tools', name: 'Tools' },
    { id: 'account', name: 'Account' },
    { id: 'technical', name: 'Technical' },
  ];

  const faqs = [
    {
      id: 1,
      category: 'general',
      question: 'What is Skincare Compass?',
      answer: 'Skincare Compass is a comprehensive educational platform that provides science-based information about skincare ingredients, their combinations, and how to build effective routines. We bridge the gap between complex dermatological research and practical skincare application.',
    },
    {
      id: 2,
      category: 'general',
      question: 'Is the information on Skincare Compass medically accurate?',
      answer: 'Yes, all our content is reviewed by skincare experts and based on peer-reviewed scientific research. However, our platform is for educational purposes only and should not replace professional medical advice.',
    },
    {
      id: 3,
      category: 'ingredients',
      question: 'How do you determine ingredient compatibility?',
      answer: 'Our compatibility assessments are based on scientific literature, pH compatibility, chemical interactions, and clinical studies. We consider factors like stability, efficacy, and potential for irritation when ingredients are combined.',
    },
    {
      id: 4,
      category: 'ingredients',
      question: 'Can I suggest new ingredients to be added to the database?',
      answer: 'Absolutely! We welcome suggestions for new ingredients. You can submit requests through our website, and our research team will evaluate them based on scientific evidence and user interest.',
    },
    {
      id: 5,
      category: 'tools',
      question: 'Are the skincare tools free to use?',
      answer: 'Yes, all our basic tools including the Ingredient Compatibility Checker and Routine Builder are completely free. We believe everyone should have access to quality skincare education.',
    },
    {
      id: 6,
      category: 'tools',
      question: 'How accurate is the Compatibility Checker?',
      answer: 'Our Compatibility Checker is based on extensive research and is regularly updated. While it provides excellent guidance, individual skin reactions can vary, so we always recommend patch testing new combinations.',
    },
    {
      id: 7,
      category: 'account',
      question: 'Do I need to create an account to use the tools?',
      answer: 'No, you can use most of our tools without creating an account. However, having an account allows you to save your results, track your routine, and receive personalized recommendations.',
    },
    {
      id: 8,
      category: 'account',
      question: 'How do you protect my personal information?',
      answer: 'We take privacy seriously and follow industry-standard security practices. We never sell your personal information, and you can delete your account and data at any time.',
    },
    {
      id: 9,
      category: 'technical',
      question: 'Why is the website running slowly?',
      answer: 'If you experience slow loading times, try clearing your browser cache, disabling browser extensions, or switching to a different browser. If issues persist, please contact our technical support.',
    },
    {
      id: 10,
      category: 'technical',
      question: 'Can I access Skincare Compass on mobile devices?',
      answer: 'Yes, our website is fully responsive and optimized for mobile devices. You can access all features and tools from your smartphone or tablet.',
    },
    {
      id: 11,
      category: 'general',
      question: 'Do you provide personalized skincare consultations?',
      answer: 'While we don\'t provide one-on-one medical consultations, our tools and guides are designed to help you make informed decisions about your skincare routine. For specific medical concerns, we recommend consulting with a dermatologist.',
    },
    {
      id: 12,
      category: 'ingredients',
      question: 'What should I do if an ingredient causes irritation?',
      answer: 'If you experience irritation, discontinue use immediately and consult with a healthcare professional if symptoms persist. Our platform provides general information, but individual reactions can vary significantly.',
    },
  ];

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      {/* Header */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <HelpCircle className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Find answers to common questions about Skincare Compass, our tools, 
              and how to make the most of our platform.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <p className="text-gray-600">
              Showing {filteredFAQs.length} questions
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Items */}
      <div className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.length > 0 ? (
              <div className="space-y-4">
                {filteredFAQs.map((faq, index) => (
                  <div
                    key={faq.id}
                    className="card overflow-hidden animate-slide-up"
                    style={{ animationDelay: `${index * 0.05}s` }}
                  >
                    <button
                      onClick={() => toggleItem(faq.id)}
                      className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                    >
                      <h3 className="text-lg font-semibold text-brand-charcoal pr-4">
                        {faq.question}
                      </h3>
                      {openItems.includes(faq.id) ? (
                        <ChevronUp className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      )}
                    </button>
                    
                    {openItems.includes(faq.id) && (
                      <div className="px-6 pb-4 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed pt-4">
                          {faq.answer}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <HelpCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No questions found</h3>
                <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
                Still Have Questions?
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Can't find what you're looking for? Our community forum is a great place to ask questions
                and get answers from other skincare enthusiasts.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/about" className="btn-primary">
                  About Us
                </a>
                <a href="/blog" className="btn-secondary">
                  Read Our Blog
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQPage;