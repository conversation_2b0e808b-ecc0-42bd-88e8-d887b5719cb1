import React from 'react';
import { BookOpen, GraduationCap, Users, Clock, ArrowRight } from 'lucide-react';

const EducationHub: React.FC = () => {
  const guides = [
    {
      category: 'Beginner',
      title: 'Skincare 101: Your First Routine',
      description: 'Master the basics with our comprehensive guide to building your first skincare routine.',
      readTime: '8 min read',
      difficulty: 'Beginner',
      color: 'bg-category-green',
    },
    {
      category: 'Intermediate',
      title: 'Understanding Active Ingredients',
      description: 'Deep dive into retinoids, acids, and other actives for intermediate skincare enthusiasts.',
      readTime: '15 min read',
      difficulty: 'Intermediate',
      color: 'bg-category-blue',
    },
    {
      category: 'Advanced',
      title: 'pH and Formulation Science',
      description: 'Advanced concepts in skincare chemistry and how formulations affect ingredient efficacy.',
      readTime: '20 min read',
      difficulty: 'Advanced',
      color: 'bg-category-lavender',
    },
    {
      category: 'Trending',
      title: 'K-Beauty Ingredients Explained',
      description: 'Explore the science behind popular Korean skincare ingredients and their benefits.',
      readTime: '12 min read',
      difficulty: 'Intermediate',
      color: 'bg-category-pink',
    },
  ];

  const quickFacts = [
    {
      icon: Users,
      stat: '50K+',
      label: 'Students Learning',
    },
    {
      icon: BookOpen,
      stat: '200+',
      label: 'Educational Articles',
    },
    {
      icon: GraduationCap,
      stat: '15',
      label: 'Learning Paths',
    },
  ];

  return (
    <section className="section-padding bg-brand-off-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
            <GraduationCap className="w-8 h-8 text-brand-teal" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Education Hub
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From skincare basics to advanced formulation science, our educational resources 
            make complex topics accessible and actionable.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-6 mb-16">
          {quickFacts.map((fact, index) => (
            <div key={index} className="text-center animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-sm mb-4">
                <fact.icon className="w-8 h-8 text-brand-teal" />
              </div>
              <div className="text-3xl font-bold text-brand-charcoal">{fact.stat}</div>
              <div className="text-gray-600">{fact.label}</div>
            </div>
          ))}
        </div>

        {/* Featured Guides */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {guides.map((guide, index) => (
            <div
              key={index}
              className="card p-6 hover:scale-105 transition-all duration-200 cursor-pointer group animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between mb-4">
                <span className={`text-xs font-medium text-white px-3 py-1 rounded-full ${guide.color}`}>
                  {guide.category}
                </span>
                <div className="flex items-center text-gray-500 text-sm">
                  <Clock className="w-4 h-4 mr-1" />
                  {guide.readTime}
                </div>
              </div>

              <h3 className="text-xl font-semibold text-brand-charcoal mb-3 group-hover:text-brand-teal transition-colors duration-200">
                {guide.title}
              </h3>

              <p className="text-gray-600 mb-4 leading-relaxed">
                {guide.description}
              </p>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {guide.difficulty}
                </span>
                <ArrowRight className="w-5 h-5 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
              </div>
            </div>
          ))}
        </div>

        {/* Learning Paths Preview */}
        <div className="bg-white rounded-2xl p-8 shadow-sm">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
              Structured Learning Paths
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Follow our curated learning paths designed to take you from beginner to expert, 
              with hands-on exercises and practical applications.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {['Foundation Builder', 'Active Ingredient Master', 'Formulation Expert'].map((path, index) => (
              <div key={index} className="text-center p-6 border border-gray-100 rounded-lg hover:border-brand-teal transition-colors duration-200">
                <div className="w-12 h-12 bg-brand-teal/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-brand-teal font-bold">{index + 1}</span>
                </div>
                <h4 className="font-semibold text-brand-charcoal mb-2">{path}</h4>
                <p className="text-sm text-gray-600">
                  {index === 0 && "Master the fundamentals of skincare science and routine building."}
                  {index === 1 && "Deep dive into active ingredients and their mechanisms of action."}
                  {index === 2 && "Understand advanced formulation principles and ingredient interactions."}
                </p>
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <button className="btn-primary">
              Start Learning Journey
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EducationHub;