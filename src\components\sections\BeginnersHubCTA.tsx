import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BookOpen, Search, FileText, ArrowRight, GraduationCap, BarChart3, Lightbulb } from 'lucide-react';

const BeginnersHubCTA: React.FC = () => {
  const beginnerResources = [
    {
      slug: 'skincare-101',
      title: 'Skincare 101',
      description: 'Master the fundamentals of skincare with our comprehensive beginner guide. Learn the basics of cleansing, moisturizing, and sun protection.',
      icon: GraduationCap,
      color: 'bg-category-green',
      readTime: '8 min read',
      infographic: '📊 Step-by-step routine flowchart',
    },
    {
      slug: 'ingredient-dictionary',
      title: 'Ingredient Dictionary',
      description: 'Decode complex ingredient names and understand what they do. Your go-to reference for navigating skincare labels with confidence.',
      icon: Search,
      color: 'bg-category-blue',
      readTime: '12 min read',
      infographic: '🔍 Visual ingredient breakdown',
    },
    {
      slug: 'how-to-read-labels',
      title: 'How to Read Labels',
      description: 'Learn to interpret ingredient lists, understand concentrations, and identify marketing claims vs. scientific facts.',
      icon: FileText,
      color: 'bg-category-lavender',
      readTime: '10 min read',
      infographic: '📋 Label reading guide',
    },
  ];

  const infographicPreviews = [
    {
      title: 'AM vs PM Routine Visual Guide',
      description: 'Interactive flowchart showing optimal ingredient timing',
      icon: '🌅🌙',
    },
    {
      title: 'Skin Type Identification Chart',
      description: 'Visual decision tree to identify your skin type',
      icon: '🔍',
    },
    {
      title: 'Ingredient Compatibility Matrix',
      description: 'Color-coded chart of ingredient interactions',
      icon: '🧪',
    },
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-brand-teal/5 to-brand-teal-light/5">
      <div className="container-custom">
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
            <BookOpen className="w-8 h-8 text-brand-teal" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            New to Skincare? Start Here
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Begin your skincare journey with confidence. Our beginner-friendly guides 
            break down complex science into simple, actionable steps with visual learning tools.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {beginnerResources.map((resource, index) => (
            <Link
              key={resource.slug}
              to={`/beginners/${resource.slug}`}
              className="card p-8 hover:scale-105 transition-all duration-300 group animate-slide-up bg-white"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 ${resource.color} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-200`}>
                <resource.icon className="w-8 h-8 text-white" />
              </div>

              <h3 className="text-xl font-bold text-brand-charcoal mb-4 group-hover:text-brand-teal transition-colors duration-200">
                {resource.title}
              </h3>

              <p className="text-gray-600 mb-6 leading-relaxed">
                {resource.description}
              </p>

              {/* Infographic Preview */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-2 mb-2">
                  <BarChart3 className="w-4 h-4 text-brand-teal" />
                  <span className="text-sm font-medium text-brand-teal">Visual Learning</span>
                </div>
                <p className="text-sm text-gray-600">{resource.infographic}</p>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {resource.readTime}
                </span>
                <ArrowRight className="w-5 h-5 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
              </div>
            </Link>
          ))}
        </div>

        {/* Infographic Previews */}
        <div className="bg-white rounded-2xl p-8 mb-12 shadow-sm">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Lightbulb className="w-6 h-6 text-brand-teal" />
              <h3 className="text-2xl font-bold text-brand-charcoal">
                Visual Learning Tools
              </h3>
            </div>
            <p className="text-gray-600">
              Complex skincare concepts made simple through interactive charts and infographics
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            {infographicPreviews.map((infographic, index) => (
              <div key={index} className="text-center p-6 border border-gray-100 rounded-lg hover:border-brand-teal transition-colors duration-200">
                <div className="text-3xl mb-3">{infographic.icon}</div>
                <h4 className="font-semibold text-brand-charcoal mb-2">{infographic.title}</h4>
                <p className="text-sm text-gray-600">{infographic.description}</p>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link to="/infographics" className="btn-secondary">
              View All Visual Guides
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-8 text-center shadow-sm">
          <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
            Ready to Build Your First Routine?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Take our personalized quiz to get product recommendations tailored to your 
            skin type, concerns, and lifestyle.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/tools/routine-builder-quiz" className="btn-primary">
              Take Routine Quiz
            </Link>
            <Link to="/beginners" className="btn-secondary">
              Browse All Guides
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BeginnersHubCTA;