import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useLocation } from 'react-router-dom';
import { ArrowLeft, CheckCircle, AlertTriangle, XCircle, Clock, Users, Star, TrendingUp, ArrowRight } from 'lucide-react';
import { combinations } from '../data/combinations';
import MetaTags from '../components/SEO/MetaTags';

const CombinationDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const location = useLocation();
  
  // Scroll to top when navigating to a new page
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);
  
  // Get combination data from our data file
  const combination = slug ? combinations[slug] : null;

  if (!combination) {
    return (
      <div className="pt-16 min-h-screen bg-brand-off-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-brand-charcoal mb-4">Combination Not Found</h1>
          <Link to="/combinations" className="btn-primary">
            Browse All Combinations
          </Link>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'caution':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'avoid':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return CheckCircle;
      case 'caution':
        return AlertTriangle;
      case 'avoid':
        return XCircle;
      default:
        return CheckCircle;
    }
  };

  const StatusIcon = getStatusIcon(combination.status);

  // SEO Data
  const seoTitle = `${combination.ingredients.join(' + ')}: Complete Combination Guide & How to Layer`;
  const seoDescription = `Expert guide to combining ${combination.ingredients.join(' and ')} in skincare. Learn layering order, benefits, compatibility, and best products for this powerful combination.`;
  const seoKeywords = `${combination.ingredients.join(', ')}, skincare combination, ingredient layering, ${combination.ingredients.join(' + ')}, skincare routine, ingredient compatibility`;
  const canonicalUrl = `https://www.skincarecompass.com/combinations/${slug}/`;

  // Structured Data for HowTo Schema
  const howToStructuredData = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": `How to Layer ${combination.ingredients.join(' + ')}`,
    "description": combination.description,
    "image": `https://www.skincarecompass.com/images/combinations/${slug}.jpg`,
    "totalTime": "PT10M",
    "estimatedCost": {
      "@type": "MonetaryAmount",
      "currency": "USD",
      "value": "50-150"
    },
    "supply": combination.ingredients.map(ingredient => ({
      "@type": "HowToSupply",
      "name": `${ingredient} product`
    })),
    "step": combination.howToLayer.map(step => ({
      "@type": "HowToStep",
      "name": step.title,
      "text": step.description,
      "position": step.step
    }))
  };

  // Article Structured Data
  const articleStructuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": seoTitle,
    "description": seoDescription,
    "image": `https://www.skincarecompass.com/images/combinations/${slug}.jpg`,
    "author": {
      "@type": "Organization",
      "name": "Skincare Compass"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Skincare Compass",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.skincarecompass.com/logo.png"
      }
    },
    "datePublished": "2024-12-15",
    "dateModified": "2024-12-15",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    }
  };

  // Get related combinations
  const getRelatedCombinations = () => {
    return Object.values(combinations)
      .filter(combo => 
        combo.slug !== combination.slug && 
        combo.ingredients.some(ing => 
          combination.ingredients.includes(ing) || 
          combination.ingredients.some(cIng => ing.includes(cIng))
        )
      )
      .slice(0, 2);
  };

  const relatedCombinations = getRelatedCombinations();

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        canonicalUrl={canonicalUrl}
        structuredData={[howToStructuredData, articleStructuredData]}
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link to="/" className="text-gray-500 hover:text-brand-teal">Home</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link to="/combinations" className="text-gray-500 hover:text-brand-teal">Combinations</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-brand-charcoal font-medium">{combination.ingredients.join(' + ')}</li>
            </ol>
          </nav>
          <Link
            to="/combinations"
            className="inline-flex items-center space-x-2 text-brand-teal hover:text-brand-teal-dark transition-colors duration-200 mt-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Combinations</span>
          </Link>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white">
        <div className="container-custom py-12">
          <div className="max-w-4xl mx-auto">
            <div className={`card p-8 border-2 ${getStatusColor(combination.status)} mb-8`}>
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <StatusIcon className="w-8 h-8" />
                  <div>
                    <h1 className="text-4xl font-bold text-brand-charcoal mb-2">
                      Power Pairing: {combination.ingredients.join(' + ')}
                    </h1>
                    <p className="text-xl font-medium">{combination.title}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2 mb-2">
                    {combination.isTrending && (
                      <TrendingUp className="w-5 h-5 text-orange-500" />
                    )}
                    <span className="text-sm text-gray-600">Popularity: {combination.popularity}%</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm text-gray-600">{combination.userRating}/5</span>
                  </div>
                </div>
              </div>

              <p className="text-lg text-gray-600 leading-relaxed">
                {combination.description}
              </p>
            </div>

            {/* Individual Ingredients */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              {combination.ingredients.map((ingredient, index) => (
                <Link
                  key={index}
                  to={`/ingredients/${ingredient.toLowerCase().replace(' ', '-')}`}
                  className="card p-6 hover:scale-105 transition-all duration-200 group"
                  aria-label={`Learn more about ${ingredient}`}
                >
                  <h3 className="text-lg font-semibold text-brand-charcoal group-hover:text-brand-teal transition-colors duration-200 mb-2">
                    {ingredient}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Learn more about this ingredient →
                  </p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* The Synergy */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              The Synergy
            </h2>
            <div className="card p-8">
              <p className="text-gray-600 leading-relaxed text-lg">
                {combination.synergy}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Combined Benefits */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Combined Benefits
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {combination.combinedBenefits.map((benefit, index) => (
                <div key={index} className="card p-6">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{benefit}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* How to Layer */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              How to Layer (Step-by-Step Guide)
            </h2>
            <div className="space-y-6">
              {combination.howToLayer.map((step, index) => (
                <div key={index} className="card p-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-brand-teal rounded-full flex items-center justify-center text-white font-bold flex-shrink-0">
                      {step.step}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-brand-charcoal">
                          {step.title}
                        </h3>
                        <span className="text-2xl">{step.icon}</span>
                      </div>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Who Should Use This */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Who Should Use This?
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-4">Ideal For</h3>
                  <ul className="space-y-2">
                    {combination.whoShouldUse.idealFor.map((item, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-4">Skin Types</h3>
                  <div className="flex flex-wrap gap-2">
                    {combination.whoShouldUse.skinTypes.map((type, index) => (
                      <span key={index} className="text-sm bg-green-100 text-green-800 px-3 py-1 rounded-full">
                        {type}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-4">Best for Concerns</h3>
                  <div className="flex flex-wrap gap-2">
                    {combination.whoShouldUse.concerns.map((concern, index) => (
                      <span key={index} className="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                        {concern}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="card p-6 border-2 border-yellow-200 bg-yellow-50">
                  <h3 className="text-lg font-semibold text-yellow-800 mb-4">Important Notes</h3>
                  <ul className="space-y-2">
                    {combination.whoShouldUse.cautions.map((caution, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <span className="text-yellow-700">{caution}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Clinical Data & Research */}
      {(combination.clinicalData || combination.researchBacking) && (
        <div className="section-padding bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
                Clinical Evidence
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {combination.clinicalData && (
                  <div className="card p-6 border-2 border-blue-200 bg-blue-50">
                    <h3 className="text-lg font-semibold text-blue-800 mb-3">Clinical Data</h3>
                    <p className="text-blue-700">{combination.clinicalData}</p>
                  </div>
                )}
                {combination.researchBacking && (
                  <div className="card p-6 border-2 border-green-200 bg-green-50">
                    <h3 className="text-lg font-semibold text-green-800 mb-3">Research Backing</h3>
                    <p className="text-green-700">{combination.researchBacking}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Related Combinations */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Related Combinations
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {relatedCombinations.map((relatedCombo, index) => (
                <Link 
                  key={index} 
                  to={`/combinations/${relatedCombo.slug}`} 
                  className="card p-6 hover:scale-105 transition-all duration-200 group"
                >
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200">
                    {relatedCombo.ingredients.join(' + ')}
                  </h3>
                  <p className="text-sm text-gray-500 mb-2">{relatedCombo.title}</p>
                  <p className="text-gray-600 text-sm mb-3">
                    {relatedCombo.description.substring(0, 120)}...
                  </p>
                  <ArrowRight className="w-4 h-4 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CombinationDetailPage;