import React from 'react';
import { TrendingUp, Users, BookOpen } from 'lucide-react';
import { Link } from 'react-router-dom';

const Hero: React.FC = () => {
  const stats = [
    { icon: BookOpen, label: 'Ingredients', value: 'A-Z' },
    { icon: TrendingUp, label: 'Combinations', value: '10+' },
    { icon: Users, label: 'Community', value: '50K+' },
  ];

  return (
    <section className="pt-24 pb-16 bg-gradient-to-br from-brand-off-white to-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-brand-charcoal mb-6">
              Your Guide to
              <span className="text-gradient block">Skincare Science</span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              The ultimate resource for understanding ingredients, how they work, and how to use them together. 
              Make informed decisions with science-backed information.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Link to="/ingredients" className="btn-primary">
                Explore Ingredients
              </Link>
              <Link to="/beginners" className="btn-secondary">
                Learn the Basics
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-brand-teal/10 rounded-lg mb-3">
                    <stat.icon className="w-6 h-6 text-brand-teal" />
                  </div>
                  <div className="text-2xl font-bold text-brand-charcoal">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Visual */}
          <div className="relative animate-fade-in">
            <div className="relative bg-white rounded-2xl p-8 shadow-2xl">
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-brand-teal rounded-full animate-float"></div>
              <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-category-pink rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
              
              {/* Ingredient Showcase */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-brand-charcoal mb-4">Popular Ingredients</h3>
              </div>

              {/* Sample Results */}
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-brand-teal rounded-full"></div>
                    <span className="font-medium">Retinol</span>
                  </div>
                  <span className="text-sm text-gray-500">Anti-aging</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-category-pink rounded-full"></div>
                    <span className="font-medium">Vitamin C</span>
                  </div>
                  <span className="text-sm text-gray-500">Antioxidant</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-category-lavender rounded-full"></div>
                    <span className="font-medium">Niacinamide</span>
                  </div>
                  <span className="text-sm text-gray-500">Barrier Support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;