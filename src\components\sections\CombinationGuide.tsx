import React from 'react';
import { CheckCircle, XCircle, AlertTriangle, TrendingUp } from 'lucide-react';

const CombinationGuide: React.FC = () => {
  const combinations = [
    {
      ingredients: ['Vitamin C', 'Niacinamide'],
      status: 'excellent',
      icon: CheckCircle,
      effect: 'Brightening powerhouse',
      description: 'Vitamin C and Niacinamide work synergistically to brighten skin and reduce hyperpigmentation.',
      benefits: ['Enhanced brightening', 'Reduced irritation', 'Improved stability'],
    },
    {
      ingredients: ['Retinol', 'Hyaluronic Acid'],
      status: 'excellent',
      icon: CheckCircle,
      effect: 'Anti-aging with hydration',
      description: 'Hyaluronic Acid helps counteract the drying effects of Retinol while maintaining efficacy.',
      benefits: ['Reduced dryness', 'Enhanced tolerance', 'Better results'],
    },
    {
      ingredients: ['AHA', 'BHA'],
      status: 'caution',
      icon: AlertTriangle,
      effect: 'Proceed with care',
      description: 'Can be effective but may cause over-exfoliation. Start slowly and monitor skin response.',
      benefits: ['Deep exfoliation', 'Requires caution', 'Start gradually'],
    },
    {
      ingredients: ['Retinol', 'Vitamin C'],
      status: 'avoid',
      icon: XCircle,
      effect: 'Avoid simultaneous use',
      description: 'Different pH requirements and potential for irritation make this combination challenging.',
      benefits: ['Use separately', 'AM/PM routine', 'Alternate days'],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'caution':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'avoid':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return CheckCircle;
      case 'caution':
        return AlertTriangle;
      case 'avoid':
        return XCircle;
      default:
        return CheckCircle;
    }
  };

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
            <TrendingUp className="w-8 h-8 text-brand-teal" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Ingredient Combination Intelligence
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover which ingredients work together for maximum effectiveness and which combinations to approach with caution.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {combinations.map((combo, index) => {
            const StatusIcon = getStatusIcon(combo.status);
            return (
              <div
                key={index}
                className={`card p-6 border-2 ${getStatusColor(combo.status)} animate-slide-up`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <StatusIcon className="w-6 h-6" />
                    <div>
                      <h3 className="font-semibold text-lg">
                        {combo.ingredients.join(' + ')}
                      </h3>
                      <p className="text-sm font-medium">{combo.effect}</p>
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 mb-4 leading-relaxed">
                  {combo.description}
                </p>

                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-700">Key Points:</div>
                  <div className="flex flex-wrap gap-2">
                    {combo.benefits.map((benefit, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-white px-3 py-1 rounded-full border border-gray-200"
                      >
                        {benefit}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
            Want Personalized Combination Advice?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our advanced combination checker analyzes your entire routine and provides personalized recommendations based on your skin type and concerns.
          </p>
          <button className="btn-primary">
            Try Combination Checker
          </button>
        </div>
      </div>
    </section>
  );
};

export default CombinationGuide;