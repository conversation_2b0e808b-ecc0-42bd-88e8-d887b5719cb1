@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-brand-off-white text-brand-charcoal;
    font-size: 16px;
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-inter font-semibold;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  p {
    @apply text-base leading-relaxed;
  }
}

@layer components {
  .btn-primary {
    @apply bg-brand-teal text-white px-6 py-3 rounded-lg font-medium hover:bg-brand-teal-dark transition-all duration-200 shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-white text-brand-teal border-2 border-brand-teal px-6 py-3 rounded-lg font-medium hover:bg-brand-teal hover:text-white transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-brand-teal to-brand-teal-light bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  /* Ad styling */
  .ad-banner {
    @apply rounded-lg overflow-hidden;
  }

  .native-ad {
    @apply rounded-lg;
  }

  /* Ensure ads don't break layout */
  .ad-banner iframe,
  .native-ad iframe {
    @apply max-w-full;
  }

  /* Blog Content Styling */
  .blog-content {
    @apply text-gray-700 leading-relaxed;
    font-size: 16px;
    line-height: 1.7;
  }

  .blog-content h3 {
    @apply text-xl font-semibold text-brand-charcoal mt-10 mb-6;
    line-height: 1.3;
  }

  .blog-content p {
    @apply mb-6 text-gray-700;
    line-height: 1.7;
  }

  .blog-content ul {
    @apply mb-8 space-y-3 pl-0;
  }

  .blog-content li {
    @apply text-gray-700 pl-6;
    position: relative;
    line-height: 1.7;
  }

  .blog-content li::before {
    content: "•";
    @apply text-brand-teal font-bold text-lg;
    position: absolute;
    left: 0;
    top: 0;
  }

  .blog-content strong {
    @apply font-semibold text-brand-charcoal;
  }

  .blog-content img {
    @apply rounded-xl mb-8 w-full;
  }

  .blog-content em {
    @apply text-gray-600 text-sm italic block text-center mb-10;
  }

  /* First paragraph styling */
  .blog-content p:first-of-type {
    @apply text-lg leading-relaxed mb-8;
  }

  /* Table of Contents Styling */
  .toc-item {
    @apply text-gray-700 hover:text-brand-teal transition-colors duration-200 text-sm leading-relaxed;
  }

  .toc-item:hover {
    @apply text-brand-teal;
  }
}