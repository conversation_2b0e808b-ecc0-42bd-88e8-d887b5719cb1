import React, { useEffect, useRef, useState } from 'react';
import { loadAdScript, isAdBlockerActive, validateAdConfig } from '../../utils/adUtils';

interface NativeAdProps {
  containerId: string;
  scriptSrc: string;
  className?: string;
  fallbackContent?: React.ReactNode;
}

const NativeAd: React.FC<NativeAdProps> = ({
  containerId,
  scriptSrc,
  className = '',
  fallbackContent
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isAdBlockerDetected, setIsAdBlockerDetected] = useState(false);

  useEffect(() => {
    if (!containerRef.current) return;

    // Check for ad blocker
    if (isAdBlockerActive()) {
      setIsAdBlockerDetected(true);
      setHasError(true);
      return;
    }

    // Validate configuration
    const config = {
      key: containerId,
      format: 'native' as const,
      containerId,
      scriptSrc
    };

    if (!validateAdConfig(config)) {
      console.error('Invalid native ad configuration:', config);
      setHasError(true);
      return;
    }

    try {
      // Clear any existing content
      containerRef.current.innerHTML = '';

      // Create the container div with the specified ID
      const adContainer = document.createElement('div');
      adContainer.id = containerId;
      containerRef.current.appendChild(adContainer);

      // Load ad script
      loadAdScript(config)
        .then(() => setIsLoaded(true))
        .catch((error) => {
          console.error('Error loading native ad:', error);
          setHasError(true);
        });

    } catch (error) {
      console.error('Error setting up native ad:', error);
      setHasError(true);
    }
  }, [containerId, scriptSrc]);

  if (hasError) {
    if (fallbackContent) {
      return <div className={className}>{fallbackContent}</div>;
    }

    return (
      <div className={`native-ad ${className} bg-gray-50 border border-gray-200 rounded-lg p-4`}>
        <div className="text-center text-gray-500 text-sm">
          {isAdBlockerDetected ? 'Content blocked' : 'Content unavailable'}
        </div>
      </div>
    );
  }

  return (
    <div className={`native-ad ${className}`}>
      <div ref={containerRef} />
      {!isLoaded && !hasError && (
        <div className="flex items-center justify-center bg-gray-100 text-gray-500 text-sm p-4 animate-pulse rounded-lg">
          Loading content...
        </div>
      )}
    </div>
  );
};

export default NativeAd;
