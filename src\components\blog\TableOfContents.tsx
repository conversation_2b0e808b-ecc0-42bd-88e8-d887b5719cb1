import React from 'react';
import { List } from 'lucide-react';

interface TableOfContentsProps {
  content: string;
  className?: string;
}

interface TOCItem {
  id: string;
  text: string;
  level: number;
}

const TableOfContents: React.FC<TableOfContentsProps> = ({ content, className = '' }) => {
  // Extract table of contents from content
  const extractTOC = (content: string): TOCItem[] => {
    const tocItems: TOCItem[] = [];

    // Look for the "In This Article" section
    const tocMatch = content.match(/\*\*In This Article\*\*\s*([\s\S]*?)(?=\*\*[^*]+\*\*|$)/);

    if (tocMatch) {
      const tocSection = tocMatch[1];
      const lines = tocSection.split('\n').filter(line => line.trim());

      lines.forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('•') && trimmedLine.includes('[') && trimmedLine.includes('](#')) {
          // Extract the link text and anchor
          const linkMatch = trimmedLine.match(/\[([^\]]+)\]\(#([^)]+)\)/);
          if (linkMatch) {
            const [, text, id] = linkMatch;
            tocItems.push({
              id,
              text: text.trim(),
              level: 1
            });
          }
        }
      });
    }

    // If no TOC section found, extract from headings
    if (tocItems.length === 0) {
      const headingMatches = content.match(/\*\*([^*]+)\*\*/g);
      if (headingMatches) {
        headingMatches.forEach(match => {
          const headingText = match.slice(2, -2);
          // Skip "In This Article" heading
          if (headingText !== 'In This Article') {
            const anchorId = headingText.toLowerCase()
              .replace(/[^a-z0-9\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-|-$/g, '');
            tocItems.push({
              id: anchorId,
              text: headingText,
              level: 1
            });
          }
        });
      }
    }

    return tocItems;
  };

  const tocItems = extractTOC(content);

  if (tocItems.length === 0) {
    return null;
  }

  const handleClick = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className={`bg-gray-50 rounded-2xl p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-4">
        <div className="flex items-center justify-center w-8 h-8 bg-brand-teal/10 rounded-full">
          <List className="w-4 h-4 text-brand-teal" />
        </div>
        <h3 className="text-lg font-semibold text-brand-charcoal">
          In This Article
        </h3>
      </div>
      
      <nav>
        <ul className="space-y-3">
          {tocItems.map((item, index) => (
            <li key={index}>
              <button
                onClick={() => handleClick(item.id)}
                className="text-left text-gray-700 hover:text-brand-teal transition-colors duration-200 text-sm leading-relaxed block w-full"
              >
                • {item.text}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default TableOfContents;
