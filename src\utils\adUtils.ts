// Utility functions for ad management

export interface AdConfig {
  key: string;
  format: 'iframe' | 'native';
  width?: number;
  height?: number;
  containerId?: string;
  scriptSrc?: string;
}

// Track loaded scripts to avoid duplicates
const loadedScripts = new Set<string>();

export const loadAdScript = (config: AdConfig): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    try {
      // Check if script is already loaded
      if (loadedScripts.has(config.key)) {
        resolve(true);
        return;
      }

      if (config.format === 'iframe') {
        // Set up iframe ad configuration
        (window as any).atOptions = {
          'key': config.key,
          'format': 'iframe',
          'height': config.height || 300,
          'width': config.width || 160,
          'params': {}
        };

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = `//www.highperformanceformat.com/${config.key}/invoke.js`;
        script.onload = () => {
          loadedScripts.add(config.key);
          resolve(true);
        };
        script.onerror = () => reject(new Error(`Failed to load ad script: ${config.key}`));
        
        document.head.appendChild(script);
      } else if (config.format === 'native' && config.scriptSrc) {
        const script = document.createElement('script');
        script.async = true;
        script.setAttribute('data-cfasync', 'false');
        script.src = config.scriptSrc;
        script.onload = () => {
          loadedScripts.add(config.key);
          resolve(true);
        };
        script.onerror = () => reject(new Error(`Failed to load native ad script: ${config.key}`));
        
        document.head.appendChild(script);
      } else {
        reject(new Error('Invalid ad configuration'));
      }
    } catch (error) {
      reject(error);
    }
  });
};

// Clean up ad scripts when component unmounts
export const cleanupAdScript = (key: string) => {
  loadedScripts.delete(key);
  
  // Remove script elements
  const scripts = document.querySelectorAll(`script[src*="${key}"]`);
  scripts.forEach(script => script.remove());
};

// Check if ad blocker is present
export const isAdBlockerActive = (): boolean => {
  try {
    // Create a test element that ad blockers typically block
    const testAd = document.createElement('div');
    testAd.innerHTML = '&nbsp;';
    testAd.className = 'adsbox';
    testAd.style.position = 'absolute';
    testAd.style.left = '-10000px';
    document.body.appendChild(testAd);
    
    const isBlocked = testAd.offsetHeight === 0;
    document.body.removeChild(testAd);
    
    return isBlocked;
  } catch {
    return false;
  }
};

// Validate ad configuration
export const validateAdConfig = (config: AdConfig): boolean => {
  if (!config.key) return false;
  
  if (config.format === 'iframe') {
    return !!(config.width && config.height);
  }
  
  if (config.format === 'native') {
    return !!(config.containerId && config.scriptSrc);
  }
  
  return false;
};
